<!DOCTYPE html>
<html lang="en-US">

<head>
    <meta charset="utf-8">
    <meta name="google" content="notranslate">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Eagle Seven</title>
    <link rel="shortcut icon" href="static/picture/favicon.ico">
    <link rel="icon" href="static/picture/favicon.ico">
    <meta name='robots' content='max-image-preview:large'>
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
        
        html {
            scroll-behavior: smooth;
        }
    </style>
    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = {
            "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/",
            "ext": ".png",
            "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/",
            "svgExt": ".svg",
            "source": {
                "concatemoji": "\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"
            }
        };
        /*! This file is auto-generated */
        ! function(s, n) {
            var o, i, e;

            function c(e) {
                try {
                    var t = {
                        supportTests: e,a
                        timestamp: (new Date).valueOf()
                    };
                    sessionStorage.setItem(o, JSON.stringify(t))
                } catch (e) {}
            }

            function p(e, t, n) {
                e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0);
                var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data),
                    a = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data));
                return t.every(function(e, t) {
                    return e === a[t]
                })
            }

            function u(e, t) {
                e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0);
                for (var n = e.getImageData(16, 16, 1, 1), a = 0; a < n.data.length; a++)
                    if (0 !== n.data[a]) return !1;
                return !0
            }

            function f(e, t, n, a) {
                switch (t) {
                    case "flag":
                        return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\udde8\ud83c\uddf6", "\ud83c\udde8\u200b\ud83c\uddf6") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");
                    case "emoji":
                        return !a(e, "\ud83e\udedf")
                }
                return !1
            }

            function g(e, t, n, a) {
                var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : s.createElement("canvas"),
                    o = r.getContext("2d", {
                        willReadFrequently: !0
                    }),
                    i = (o.textBaseline = "top", o.font = "600 32px Arial", {});
                return e.forEach(function(e) {
                    i[e] = t(o, e, n, a)
                }), i
            }

            function t(e) {
                var t = s.createElement("script");
                t.src = e, t.defer = !0, s.head.appendChild(t)
            }
            "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", i = ["flag", "emoji"], n.supports = {
                everything: !0,
                everythingExceptFlag: !0
            }, e = new Promise(function(e) {
                s.addEventListener("DOMContentLoaded", e, {
                    once: !0
                })
            }), new Promise(function(t) {
                var n = function() {
                    try {
                        var e = JSON.parse(sessionStorage.getItem(o));
                        if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests
                    } catch (e) {}
                    return null
                }();
                if (!n) {
                    if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try {
                        var e = "postMessage(" + g.toString() + "(" + [JSON.stringify(i), f.toString(), p.toString(), u.toString()].join(",") + "));",
                            a = new Blob([e], {
                                type: "text/javascript"
                            }),
                            r = new Worker(URL.createObjectURL(a), {
                                name: "wpTestEmojiSupports"
                            });
                        return void(r.onmessage = function(e) {
                            c(n = e.data), r.terminate(), t(n)
                        })
                    } catch (e) {}
                    c(n = g(i, f, p, u))
                }
                t(n)
            }).then(function(e) {
                for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]);
                n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function() {
                    n.DOMReady = !0
                }
            }).then(function() {
                return e
            }).then(function() {
                var e;
                n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji)))
            }))
        }((window, document), window._wpemojiSettings);
        /* ]]> */
    </script>
    <style id='wp-emoji-styles-inline-css' type='text/css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <link rel='stylesheet' id='wp-block-library-css' href='static/css/style.min.css' type='text/css' media='all'>
    <style id='classic-theme-styles-inline-css' type='text/css'>
        /*! This file is auto-generated */
        
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }
        
        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id='global-styles-inline-css' type='text/css'>
         :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }
        
         :where(.is-layout-flex) {
            gap: 0.5em;
        }
        
         :where(.is-layout-grid) {
            gap: 0.5em;
        }
        
        body .is-layout-flex {
            display: flex;
        }
        
        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }
        
        .is-layout-flex> :is(*, div) {
            margin: 0;
        }
        
        body .is-layout-grid {
            display: grid;
        }
        
        .is-layout-grid> :is(*, div) {
            margin: 0;
        }
        
         :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }
        
         :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }
        
         :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }
        
         :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }
        
        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }
        
        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }
        
        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }
        
        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }
        
        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }
        
        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }
        
        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }
        
        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }
        
        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }
        
        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }
        
        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }
        
        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }
        
        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }
        
        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }
        
        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }
        
        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }
        
        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }
        
        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }
        
        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }
        
        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }
        
        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }
        
        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }
        
        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }
        
        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }
        
        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }
        
        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }
        
        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }
        
        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }
        
        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }
        
        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }
        
        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }
        
        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }
        
        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }
        
        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }
        
        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }
        
        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }
        
        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }
        
        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }
        
        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }
        
        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }
        
        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }
        
        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }
        
        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }
        
        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }
        
        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }
        
        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }
        
        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }
        
        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }
        
        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }
        
        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }
        
        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }
        
        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }
        
         :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }
        
         :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }
        
         :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }
        
         :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }
        
         :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>
    <link rel='stylesheet' id='contact-form-7-css' href='static/css/styles.css' type='text/css' media='all'>
    <link rel='stylesheet' id='app-styles-xpro-css' href='static/css/app.css' type='text/css' media='all'>
    <script type="text/javascript" src="static/js/jquery.min.js" id="jquery-core-js"></script>
    <script type="text/javascript" src="static/js/jquery-migrate.min.js" id="jquery-migrate-js"></script>
    <link rel="https://api.w.org/" href="/index.php?rest_route=/">
    <link rel="EditURI" type="application/rsd+xml" title="RSD" href="xmlrpc.xml">
    <meta name="generator" content="WordPress 6.8.2">

    <link rel='shortlink' href='/'>


</head>

<body class="home wp-singular page-template page-template-templates page-template-home-page page-template-templateshome-page-php page page-id-35 wp-theme-eagleseven-theme">



    <main id="main" class="site-main o-wrapper">

        <!-- Hero Area -->
        <section class="lead-section grid" style="background-image: url('static/picture/Lead-bg-copy-min.jpg')">
            <div class="header--holder">

                <!-- Header Menu -->
                <header class="o-wrapper c-header is-fixed">
                    <div class="container">
                        <div class="top--logo-container">
                            <a href="" rel="home">
                                <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 414 89.12"><path d="M0 .09c16.31 0 32.62.09 48.93 0a8.15 8.15 0 0 1 7.72 4.14c8 12.05 16.23 23.9 24.64 36.18-6.91 12.49-13.7 25-20.8 37.4-2.17 3.77-3.2 9.21-7.8 11.33H18.44l12-22.83.59-1q3.78-6.72 7.56-13.5a22.12 22.12 0 0 1 14.15-10.9c6-1.64 12.16 0 18.51-1.22-4.16-8.52-9.82-13.93-19.67-13.69-12 .21-23.92 0-35.5 0-3.22-3-4.67-6.46-6.85-9.37l-.52-.77-.69-1-.6-1-.19-.28-.54-.82-.6-.92-.75-1L4 8.9l-.43-1-1.46-2-.54-1L0 3.09z" fill="#a61c34"></path><path d="M414 38.76c-4.75.6-10.53.3-15.48.35-13.62.13-27.24 0-41.16 0v-39H414v8.16h-47.08v7.06h41c.07 3 .47 5.39-.31 8.1h-40.74c0 2.42-.59 4.49.18 6.64h47v2c-.05 2.43-.26 4.48-.05 6.69z"></path><path d="M293 89.09V50.7h55.53a16.55 16.55 0 0 1-.13 8h-45.2a7.05 7.05 0 0 0-.12 7h38.76c1.8 2.49 1.29 4.59.8 7.42h-39.1c-1.87 2.5-1.48 4.4-.81 6.81h45.14c1.83 3.21.62 6.3 1.13 9.17zm-199 0l.4-9h44.72c2.24-1.92 2.26-3.45 1.49-5.25a18.79 18.79 0 0 0-8.61-1.62c-9.32 0-18.64.08-28 0-6.67-.05-11.53-4-12-9.55-.55-6.75 2.85-11.26 9.82-12.78a31 31 0 0 1 6.41-.65c13.29-.06 26.58 0 40 0 1.09 2.85.78 5.42.32 8.74-7.78 0-15.55-.2-23.3.06s-15.41-1.28-22.83 1.09c-1 5 1.5 5.85 5 5.85 10.32 0 20.64-.12 31 .09 9 .18 13.54 5.3 12.65 13.52-.59 5.51-5 7.32-9 9.51zm65 0V50.51h54.49c1.14 2.58.44 5.16.61 8.22h-44.94c-1.67 2.44-1.22 4.51-.65 7.16H208c1.37 2.58.76 4.64.66 7.28h-39.79c-1.24 2.44-1 4.35-.36 6.93h44.93c1.06 3.39.55 6.2.55 9zm196 0v-38c2.79-1.55 5.61-.62 8.22-.8l40.61 27.17c2.1-9.12.1-17.9 1.26-26.48 2.93-1.47 6-.31 8.91-.86v39h-10c-12.51-9.7-26.09-17.8-40-27.41v27.38zm-107 0c-6.15-9.89-14.27-18.27-21.15-27.61-2.53-3.44-5.24-6.74-8.19-10.52 4-1.35 7.39-.55 11.28-.68l23.77 30.2 23.27-30h11.88L259 89.09z" fill="#ababad"></path><path d="M286.46 15.63v23.13c-14.53 0-28.9 1-43-.28-15.57-1.48-22.85-15.94-15.75-28.65 3.89-7.07 10.73-9.33 18-9.6C258-.21 270.34.11 282.93.11v8.4c-8.2 0-16.33-.06-24.45 0-4.31 0-8.63.22-12.93.57-5.62.46-9.2 3.62-9.72 8.25-.78 6.94 1.13 10.13 7.43 12.12a16.93 16.93 0 0 0 4.88.82c9.47.08 18.93 0 28.6 0a13.83 13.83 0 0 0-.06-7h-18.26a20.38 20.38 0 0 1-.18-7.72zM95.16.28h55.71v8H105c-1.42 2.33-.62 4.34-.9 6.87h40.57c1 2.86.48 5.18.58 8h-40.94v6.86h46.41v8.83H95.16zM172 32.76l-5 6.11h-11.9L185.66.21h10.57l30.56 38.6h-11.9l-5-6zM190.9 8.4l-12.6 16.28h25.39zm158.73 21.93v8.43H294.5V.34h9.4v30z"></path></svg>
                            </a>
                        </div>
                        <div class="hamburger-menu">
                            <div class="bar"></div>
                            <div class="bar"></div>
                            <div class="bar"></div>
                        </div>
                        <nav class="o-wrapper is-hidden">
                            <ul class="c-main-nav scroll-to">
                                <li class="about-us"><a href="#about-us-section-grid">about us</a></li>
                                <li class="what-we-do"><a href="#what-we-do-section-grid">what we do</a></li>
                                <li class="careers"><a href="#careers-section-grid">careers</a></li>
                                <li class="contact-us"><a href="#contact-us-section-grid">contact us</a></li>
                            </ul>
                        </nav>
                    </div>
                </header>
                <span class="overlay is-hidden"></span>
            </div>

            <div class="heading">
                <h1 class="carousel-title">Independent Thinkers.</h1>
                <h1 class="carousel-title">Superior Technology.</h1>
                <h1 class="carousel-title">Global Reach.</h1>
                <h1 class="carousel-title">Outstanding Results.</h1>
            </div>

        </section>

        <!-- About Us -->
        <section class="about-us-section grid">
            <div id="about-us-section-grid" class="container">

                <div class="col-l-3">
                    <h3 class="header type--heading type--underline-light-grey type--red"> About Us </h3>
                </div>

                <div class="grid col-l-9">



                    <article class="margin--bottom-m col-s-6">
                        <h4 class="subheader type--red">Company </h4>
                        <span class="col-l-10">We are a privately held proprietary trading firm specializing in providing liquidity to financial markets in the US and around the world. We combine the latest technology and robust risk management with a talented group of professional traders to employ a disciplined and adaptable approach to trading across a diverse range of asset classes. </span>
                    </article>




                    <article class="margin--bottom-m col-s-6">
                        <h4 class="subheader type--red">Culture </h4>
                        <span class="col-l-10">We are a team of extraordinary people who have created an environment that is directly correlated with our firm’s success. We share a vision guided by common principles - collaboration, communication, and innovation, which empower our entrepreneurial employees to think critically, share ideas, and solve problems. Our proven track record of long term stability, our flat organizational structure, and work life balance are all essential to supporting our culture and dynamic workplace. </span>
                    </article>


                </div>
            </div>
        </section>

        <!-- What We Do -->
        <section class="what-we-do-section bg--very-light-grey grid">
            <div id="what-we-do-section-grid" class="section--content">
                <div class="container">
                    <h3 class="header type--heading type--underline-medium-grey type--red"> What We Do </h3>

                    <article class="margin--bottom-m margin--top-l  col-s-6 col-l-12">
                        <h4 class="subheader type--red ">Trading</h4>
                        <span class="col-l-11">We execute various trading strategies across listed derivatives and securities markets on numerous global exchanges. We use our own capital and do not answer to third party investors or clients. Through our passion for trading, we are committed to continually exploring new markets and generating new strategies to capitalize on emerging opportunities. </span>
                    </article>

                    <article class="margin--bottom-m margin--top-l  col-s-6 col-l-12">
                        <h4 class="subheader type--red ">Technology</h4>
                        <span class="col-l-11">Our proprietary trading platform is built to respond to the needs of an ever-changing and competitive trading environment by providing speed, innovation, transparency, and accuracy. Our technology serves the needs that span from sophisticated algorithms to outright trading and gives us the ability to implement a vast array of complex strategies and provides us with the edge required to thrive in today's dynamic trading environment.</span>
                    </article>

                </div>
            </div>
            <div class="section--img" style="background-image: url('static/picture/what-we-do-img-min.jpg');"></div>
        </section>

        <!-- Careers -->
        <section class="careers-section grid">
            <div id="careers-section-grid" class="container">
                <h3 class="header margin--top-xs margin--bottom-s type--heading type--red type--underline-light-grey"> Careers </h3>

                <div>
                    <h5> Open positions </h5>
                    <article class="col-s-6 col-12 col-l-7">
                        <ul class="option--items margin--bottom-xl col-12 col-l-11 margin--left-none">
                            <li class="option--item margin--bottom-xs is-active" data-item='item-0'>Junior Trader</li>
                            <li class="option--item margin--bottom-xs " data-item='item-1'>Senior Software Developer (Gateway/Market Data)</li>
                            <li class="option--item margin--bottom-xs " data-item='item-2'>FPGA Design Engineer</li>
                            <li class="option--item margin--bottom-xs " data-item='item-3'>Web Application Developer</li>
                            <li class="option--item margin--bottom-xs " data-item='item-4'>Energy Trader</li>
                            <li class="option--item margin--bottom-xs " data-item='item-5'>Algorithmic Trader</li>
                            <li class="option--item margin--bottom-xs " data-item='item-6'>Algorithmic Trader - Amsterdam</li>
                        </ul>
                    </article>

                    <div class="col-s-6 col-l-4">
                        <article>
                            <p>Eagle Seven is always interested in meeting qualified candidates who are interested in becoming a member of our team. If you do not see an open role that fits your profile, please apply with our General Application</p>
                        </article>

                        <div data-item='general-aplication' class="btn option--item margin--left-none margin--top-l margin--right-auto red">General Application</div>
                    </div>

                </div>
            </div>
            <div class="popup">
                <article class="is-hidden job" data-item='item-0'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">Junior Trader</span>
                        <div class="description">
                            <p>Eagle Seven, LLC is seeking a <i>Junior Trader</i> who will be responsible for supporting all aspects of trading desk operations. The right candidate will have a passion for capital markets and be a motivated self-starter.
                                <strong><i>Ability and interest to work Asia/European (overnight) hours is required. </i></strong> </p>
                            <p>Primary responsibilities include: </p>
                            <ul>
                                <li>Executing algorithmic trading strategies in accordance with the trading desk's objectives</li>
                                <li>Monitoring and reconciling trading positions consistently throughout the shift</li>
                            </ul>
                            <ul>
                                <li>Performing start of day position and activity reconciliations</li>
                                <li>Performing post trade analysis on a variety of algorithmic trading strategies</li>
                                <li>Completing end of day PnL and position reporting</li>
                                <li>Communicating with trading exchanges and internal technology groups regarding trading system issues</li>
                                <li>Managing and reporting any potential compliance issues</li>
                                <li>Assisting quantitative traders in performing research for new trading strategies</li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <ul>
                                <li>Bachelor’s degree in business/finance, statistics, mathematics, engineering, or related field </li>
                                <li>0-2 years’ work experience; financial experience preferred, but not required</li>
                                <li>Strong analytical, quantitative, and math skills</li>
                                <li>Experience with Python is required </li>
                                <li>Ability to work independently and successfully manage multiple tasks in a complex and fast-paced environment </li>
                                <li>Strong organizational skills and attention to detail</li>
                                <li>Excellent written and verbal communication skills</li>
                                <li>Excellent computer proficiency, including Excel and other MS office applications</li>
                                <li>Demonstrated strong work ethic and team player focused on contributing to the success of the trading desk</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <p>Eagle Seven offers a competitive and comprehensive benefits package to all full-time employees.</p>
                            <ul>
                                <li>Medical PPO and HMO coverage through BlueCross BlueShield</li>
                                <li>Company Contributions to a Health Savings Account (with enrollment into a High Deductible Health Plan)</li>
                                <li>Dental coverage through Principal</li>
                                <li>Vision coverage through VSP</li>
                                <li>401k Retirement Savings Plan with Employer Match</li>
                                <li>Company Paid Life Insurance</li>
                                <li>Company Paid Disability Insurance</li>
                                <li>Paid Time Off</li>
                                <li>Flexible Spending Account</li>
                                <li>Pre-tax Transit Benefits</li>
                                <li>Complimentary Lunch and Beverages</li>
                                <li>Access to Newly Renovated Building Gym and Bike Room</li>
                            </ul>
                        </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/75121'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden job" data-item='item-1'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">Senior Software Developer (Gateway/Market Data)</span>
                        <div class="description">
                            <p>Eagle Seven is seeking a <em>Senior Software Developer </em>focused on exchange connectivity and market data.&nbsp; The individual will be responsible for analyzing exchange protocols, proposing design solutions, and implementing
                                connectivity to trading venues across the world. The role be a part of the platform development team and will provide the individual with exposure to traders and strategy developers. The successful candidate will be a self-starter,
                                have strong sense of ownership and be driven to provide technical and intellectual solutions to business problems.&nbsp; </p>
                            <p>&nbsp;</p>
                            <p>Primary Responsibilities include:</p>
                            <ul>
                                <li>Architecting and implementing low-latency market access solutions</li>
                                <li>Understanding, interpreting, and interfacing with global exchanges and their protocols</li>
                                <li>Designing, developing, and supporting market data feed handlers and exchange order routers</li>
                                <li>Diagnosing latency issues and resolving with appropriate tuning and optimizations</li>
                                <li>Working with traders to source, evaluate and facilitate access to new data sources</li>
                                <li>Working with extended team to capture, house, and provide historical access to market data</li>
                                <li>Liaise with vendors on data and technical issues as needed to deliver rapid solutions to the business</li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <p>Skills and Experience:</p>
                            <ul>
                                <li>Bachelor’s degree in Computer Science or related field</li>
                                <li>Proven track record of understanding and working with global exchange protocols</li>
                                <li>Experience with writing parsers for exchange protocols such as FIX and ITCH, etc.</li>
                                <li>Strong background in C++ and C++ Template metaprogramming
                                    <a rel="nofollow noreferrer noopener" class="external"></a>with demonstrated experience using C++14/C++20</li>
                                <li>Expertise with TCP/IP, UDP multicast, sockets, network protocols, particularly on Linux/Unix systems</li>
                                <li>Experience using network tools such as Wireshark and TCPDump to monitor and debug behavior</li>
                                <li>Ability to work in a collaborative environment</li>
                                <li>Excellent written and verbal communication skills</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <p>Eagle Seven offers a competitive and comprehensive benefits package to all full-time employees.</p>
                            <ul>
                                <li>Medical PPO and HMO coverage through BlueCross BlueShield</li>
                                <li>Company Contributions to a Health Savings Account (with enrollment into a High Deductible Health Plan)</li>
                                <li>Dental coverage through Principal</li>
                                <li>Vision coverage through VSP</li>
                                <li>401k Retirement Savings Plan with Employer Match</li>
                                <li>Company Paid Life Insurance</li>
                                <li>Company Paid Disability Insurance</li>
                                <li>Paid Time Off</li>
                                <li>Flexible Spending Account</li>
                                <li>Pre-tax Transit Benefits</li>
                                <li>Complimentary Lunch and Beverages</li>
                            </ul>
                            <p></p>
                            <p><em>The minimum base salary for this role starts at $150,000. This role is eligible for a discretionary performance bonus as part of the total compensation package, in addition to the benefits listed above. Exact compensation offered may vary based on factors including, but not limited to, the candidate's experience, qualifications, and skill set.</em></p>
                            <p></p>
                        </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/314375'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden job" data-item='item-2'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">FPGA Design Engineer</span>
                        <div class="description">
                            <p>Eagle Seven is seeking a <em>FPGA Design / Verification Engineer</em> to become an integral member of the engineering team dedicated to the development of HDL solutions for our high frequency, low latency electronic trading
                                systems. The candidate will support new and existing solutions while helping define design assurance processes for programmable hardware. The role will be a part of the hardware development team and will provide the individual
                                with exposure to traders and strategy developers. Responsibilities will include developing requirements with cross-functional teams, writing HDL code targeted for design and verification, hands-on testing, and integration
                                of FPGA designs for various strategies and financial exchanges. The successful candidate will be a self-starter, have strong sense of ownership and be driven to provide technical and intellectual solutions to business problems.</p>
                            <p>Primary Responsibilities include: </p>
                            <ul>
                                <li>Researching and analyzing design concepts, processes, technologies, and/or products </li>
                                <li>Production of technical specifications, functional designs, and project documentation</li>
                                <li>Contributing to FPGA design and architecture decisions</li>
                                <li>Participating in FPGA development, simulation, debugging, and improvement processes </li>
                                <li>Implementation of FPGA design (Synthesis, Place &amp; Route, and Timing Closure) </li>
                                <li>Participating in FPGA design production release cycle (quality assurance, rollouts, maintenance and bug fixes)</li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <ul>
                                <li>Bachelor or Masters in Electrical Engineering, Computer Engineering, Computer Science or other related disciplines.</li>
                                <li>2-5 years’ of experience in FPGA design using Verilog and/or System Verilog</li>
                                <li>Experience implementing network protocols such as TCP/IP, UDP, IGMP, BGP</li>
                                <li>Experience implementing digital signal processing on FPGA and/or ASIC</li>
                                <li>Experience with FPGAs and CPLDs from vendors (Xilinx, Altera, Microsemi (Actel), and/or Lattice) </li>
                                <li>Experience with FPGA design, simulation and verification tools (Synopsys, Riviera, ModelSim, Questasim, etc.)</li>
                                <li>Experience integrating embedded systems</li>
                                <li>Experience and in-depth understandings of Data-path Pipelines, State Machines, Arithmetic Operations </li>
                                <li>Experience with advanced verification techniques (Assertion-based Verification, UVM, and Constrained Random Verification) </li>
                                <li>Programming and Scripting skills: C, C++, TCL, bash, PERL, Python, batch </li>
                                <li>Experience with exchange protocols such as FIX/Fast, OUCH, ITCH, etc.</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <p>Eagle Seven offers a competitive and comprehensive benefits package to all full-time employees.</p>
                            <ul>
                                <li>Medical PPO and HMO coverage through BlueCross BlueShield</li>
                                <li>Company Contributions to a Health Savings Account (with enrollment into a High Deductible Health Plan)</li>
                                <li>Dental coverage through Principal</li>
                                <li>Vision coverage through VSP</li>
                                <li>401k Retirement Savings Plan with Employer Match</li>
                                <li>Company Paid Life Insurance</li>
                                <li>Company Paid Disability Insurance</li>
                                <li>Paid Time Off</li>
                                <li>Flexible Spending Account</li>
                                <li>Pre-tax Transit Benefits</li>
                                <li>Complimentary Lunch and Beverages</li>
                            </ul>
                            <p></p>
                            <p><em>The minimum base salary for this role starts at $170,000. This role is eligible for a discretionary performance bonus as part of the total compensation package, in addition to the benefits listed above. Exact compensation offered may vary based on factors including, but not limited to, the candidate's experience, qualifications, and skill set.</em></p>
                        </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/317965'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden job" data-item='item-3'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">Web Application Developer</span>
                        <div class="description">
                            <p>Eagle Seven is seeking a <i>Web Application Developer</i> to work on our next generation desktop applications. The role will serve as a technical expert for all web development, firm wide architecture, and design. The successful
                                candidate should have a strong sense of good software design, an eye for aesthetics, and an interest in finance/capital markets.</p>
                            <p><strong>Primary Responsibilities include:</strong></p>
                            <ul>
                                <li>Provide architecture, design, and implementation for responsive applications across our trading, risk management, and research businesses</li>
                                <li>Develop and maintain key feature sets of the platform using industry proven design patterns, methods, and technologies</li>
                                <li>Focus on developing responsive and low latency components for large data set applications</li>
                                <li>Work with the quantitative research team to develop innovative and intuitive data visualization tools</li>
                                <li>Research and prototype new front-end technologies, tools, and visualization techniques</li>
                                <li>Facilitate testing and deployment of new features across multiple team</li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <p><strong>Skills and Experience:</strong></p>
                            <ul>
                                <li>Bachelor’s degree in Computer Science or related field</li>
                                <li> 5+ years of experience developing interactive front-end web applications</li>
                                <li>Proven track record of full stack development including HTML5, CCS3, JavaScript, and TypeScript</li>
                                <li>Experience with frameworks such as React, Angular, Node.js, Material UI and Spring Boot</li>
                                <li>Experience with SciChart a strongly desired</li>
                                <li>Experience with OpenFin strongly desired</li>
                                <li>Experience with Postgres or other relational databases</li>
                                <li>Ability to work in a collaborative, nimble, and fast paced environment</li>
                                <li>Excellent communication skills, both written and verbal </li>
                                <li>Proven ability to manage multiple projects/priorities simultaneously</li>
                                <li>Trading industry experience preferred</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <p>Eagle Seven offers a competitive and comprehensive benefits package to all full-time employees.</p>
                            <ul>
                                <li>Medical PPO and HMO coverage through BlueCross BlueShield</li>
                                <li>Company Contributions to a Health Savings Account (with enrollment into a High Deductible Health Plan)</li>
                                <li>Dental coverage through Principal</li>
                                <li>Vision coverage through VSP</li>
                                <li>401k Retirement Savings Plan with Employer Match</li>
                                <li>Company Paid Life Insurance</li>
                                <li>Company Paid Disability Insurance</li>
                                <li>Paid Time Off</li>
                                <li>Flexible Spending Account</li>
                                <li>Pre-tax Transit Benefits</li>
                                <li>Complimentary Lunch and Beverages</li>
                            </ul>
                            <p></p>
                            <p><em>The minimum base salary for this role starts at $150,000. This role is eligible for a discretionary performance bonus as part of the total compensation package, in addition to the benefits listed above. Exact compensation offered may vary based on factors including, but not limited to, the candidate's experience, qualifications, and skill set.</em></p>
                        </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/673448'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden job" data-item='item-4'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">Energy Trader</span>
                        <div class="description">
                            <p>Eagle Seven is seeking an experienced Energy Trader to trade energy futures and swap markets listed on ICE and CME. The trader will be responsible for managing all aspects of trading desk operations. The right candidate will
                                have at least four years of experience in the proprietary trading industry, a passion for capital markets, work well in a collaborative team setting and be a motivated self-starter. </p>
                            <p>Primary Responsibilities include: </p>
                            <ul>
                                <li>Operating grey and black box algorithmic trading strategies in accordance with the trading desk’s objectives</li>
                                <li>Monitoring of trading activity and positions consistently throughout the shift</li>
                                <li>Performing research and post trade analysis using the firm’s research tools on a variety of algorithmic trading strategies to improve profitability of existing models</li>
                                <li>Work with back office to solve for trade breaks, position &amp; activity reconciliation exceptions</li>
                                <li>Completing end of day PnL and position reporting</li>
                                <li>Communicating with exchanges, risk managers and internal technology groups regarding production issues</li>
                                <li>Work closely with strategy developers in order to communicate and propose certain strategy specific code changes related to execution, pricing, and risk management</li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <p>Skills and Experience: </p>
                            <ul>
                                <li>Bachelor’s degree in statistics, mathematics, engineering, business/finance, or related field </li>
                                <li>4-6 years’ work experience trading financial products (e.g options, futures)</li>
                                <li>Strong analytical, quantitative, and math skills</li>
                                <li>Experience with Python is preferred </li>
                                <li>Ability to work independently and successfully manage multiple tasks in a complex and fast-paced environment </li>
                                <li>Strong organizational skills and attention to detail</li>
                                <li>Excellent written and verbal communication skills</li>
                                <li>Demonstrated strong work ethic and team player focused on contributing to the success of the trading desk</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <p>Eagle Seven offers a competitive and comprehensive benefits package to all full-time employees.</p>
                            <ul>
                                <li>Medical PPO and HMO coverage through BlueCross BlueShield</li>
                                <li>Company Contributions to a Health Savings Account (with enrollment into a High Deductible Health Plan)</li>
                                <li>Dental coverage through Principal</li>
                                <li>Vision coverage through VSP</li>
                                <li>401k Retirement Savings Plan with Employer Match</li>
                                <li>Company Paid Life Insurance</li>
                                <li>Company Paid Disability Insurance</li>
                                <li>Paid Time Off</li>
                                <li>Flexible Spending Account</li>
                                <li>Pre-tax Transit Benefits</li>
                                <li>Complimentary Lunch and Beverages</li>
                                <li>Access to Newly Renovated Building Gym and Bike Room</li>
                            </ul>
                        </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/1276664'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden job" data-item='item-5'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">Algorithmic Trader</span>
                        <div class="description">
                            <p>Eagle Seven is seeking an experienced Algorithmic Trader to trade futures contract listed on Eurex, ICE, and CME. The trader, along with the Head of the Desk, will be responsible for managing all aspects of trading desk operations.
                                The right candidate will have a passion for capital markets, work well in a collaborative team setting, and be a motivated self-starter. </p>
                            <p>Primary Responsibilities include:</p>
                            <ul>
                                <li>Operating grey and black box algorithmic trading strategies in accordance with the trading desk’s objectives</li>
                                <li>Monitoring of trading activity and positions consistently throughout the shift</li>
                                <li>Performing research and post trade analysis using the firm’s research tools on a variety of algorithmic trading strategies to improve profitability of existing models </li>
                                <li>Applying expertise in market microstructure, historical tick data, and quantitative modeling to conduct research and develop new sources of alpha</li>
                            </ul>
                            <ul>
                                <li>Working closely with strategy developers to collaborate, propose, and test certain strategy specific code changes related to execution, pricing, and risk management</li>
                            </ul>
                            <ul>
                                <li>Communicating with risk managers and internal technology groups regarding production issues</li>
                                <li>Building and maintaining quantitative modeling tools and analytics</li>
                                <li>Working with back office to solve for trade breaks, position &amp; activity reconciliation exceptions </li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <p>Skills and Experience:</p>
                            <ul>
                                <li>Bachelor’s degree in statistics, mathematics, engineering, business/finance, or related field </li>
                                <li>4-6 years’ experience trading financial products (e.g options, futures) </li>
                            </ul>
                            <ul>
                                <li>Strong analytical, quantitative, and math skills</li>
                                <li>Experience with Python </li>
                                <li>Ability to work independently and successfully manage multiple tasks in a complex and fast-paced environment </li>
                                <li>Strong organizational skills and attention to detail</li>
                                <li>Excellent written and verbal communication skills</li>
                                <li>Demonstrated strong work ethic and team player focused on contributing to the success of the trading desk</li>
                            </ul>
                        </div>
                        <div class="benefits">
                            <p>Eagle Seven offers a competitive and comprehensive benefits package to all full-time employees.</p>
                            <ul>
                                <li>Medical PPO and HMO coverage through BlueCross BlueShield</li>
                                <li>Company Contributions to a Health Savings Account (with enrollment into a High Deductible Health Plan)</li>
                                <li>Dental coverage through Principal</li>
                                <li>Vision coverage through VSP</li>
                                <li>401k Retirement Savings Plan with Employer Match</li>
                                <li>Company Paid Life Insurance</li>
                                <li>Company Paid Disability Insurance</li>
                                <li>Paid Time Off</li>
                                <li>Flexible Spending Account</li>
                                <li>Pre-tax Transit Benefits</li>
                                <li>Complimentary Lunch and Beverages</li>
                                <li>Access to Renovated Building Gym and Bike Room</li>
                            </ul>
                        </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/3231898'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden job" data-item='item-6'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <span class="job-ti">Algorithmic Trader - Amsterdam</span>
                        <div class="description">
                            <p>Eagle Seven Europe, B.V. is seeking an experienced Algorithmic Trader for our Amsterdam office to trade futures contract listed on Eurex, ICE, and CME. The trader, along with the Head of the Desk, will be responsible for managing
                                all aspects of trading desk operations. The right candidate will have a passion for capital markets, work well in a collaborative team setting, and be a motivated self-starter. </p>
                            <p>Primary Responsibilities include:</p>
                            <ul>
                                <li>Operating grey and black box algorithmic trading strategies in accordance with the trading desk’s objectives</li>
                                <li>Monitoring of trading activity and positions consistently throughout the shift</li>
                                <li>Performing research and post trade analysis using the firm’s research tools on a variety of algorithmic trading strategies to improve profitability of existing models </li>
                                <li>Applying expertise in market microstructure, historical tick data, and quantitative modeling to conduct research and develop new sources of alpha</li>
                            </ul>
                            <ul>
                                <li>Working closely with strategy developers to collaborate, propose, and test certain strategy specific code changes related to execution, pricing, and risk management</li>
                            </ul>
                            <ul>
                                <li>Communicating with risk managers and internal technology groups regarding production issues</li>
                                <li>Building and maintaining quantitative modeling tools and analytics</li>
                                <li>Working with back office to solve for trade breaks, position &amp; activity reconciliation exceptions </li>
                            </ul>
                        </div>
                        <div class="requirements">
                            <p>Skills and Experience:</p>
                            <ul>
                                <li>Bachelor’s degree in statistics, mathematics, engineering, business/finance, or related field </li>
                                <li>4-6 years’ experience trading financial products (e.g options, futures) </li>
                            </ul>
                            <ul>
                                <li>Strong analytical, quantitative, and math skills</li>
                                <li>Experience with Python </li>
                                <li>Ability to work independently and successfully manage multiple tasks in a complex and fast-paced environment </li>
                                <li>Strong organizational skills and attention to detail</li>
                                <li>Excellent written and verbal communication skills</li>
                                <li>Demonstrated strong work ethic and team player focused on contributing to the success of the trading desk</li>
                            </ul>
                        </div>
                        <div class="benefits"> </div>
                        <div class="apply margin--top-xl"><a class="btn red margin--left-auto margin--right-auto" href='https://eagle-seven.workable.com/jobs/3231901'>Apply for this position</a></div>
                    </div>
                </article>
                <article class="is-hidden form--general-application" data-item='general-aplication'>
                    <div class="close">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                    <div class="content">
                        <h4 class="type--red "> General Application Form </h4>


                        <div class="wpcf7 no-js" id="wpcf7-f120-o1" lang="en-US" dir="ltr" data-wpcf7-id="120">
                            <div class="screen-reader-response">
                                <p role="status" aria-live="polite" aria-atomic="true"></p>
                                <ul></ul>
                            </div>
                            <form action="javascript:;" method="post" class="wpcf7-form init" aria-label="Contact form" enctype="multipart/form-data" novalidate="novalidate" data-status="init">
                                <fieldset class="hidden-fields-container"><input type="hidden" name="_wpcf7" value="120"><input type="hidden" name="_wpcf7_version" value="6.1.1"><input type="hidden" name="_wpcf7_locale" value="en_US"><input type="hidden" name="_wpcf7_unit_tag" value="wpcf7-f120-o1">
                                    <input type="hidden" name="_wpcf7_container_post" value="0"><input type="hidden" name="_wpcf7_posted_data_hash" value=""><input type="hidden" name="_wpcf7_recaptcha_response" value="">
                                </fieldset>
                                <div class="wpcf7-response-output" aria-hidden="true"></div>
                                <p><span class="wpcf7-form-control-wrap" data-name="your-name"><input size="40" maxlength="400" class="wpcf7-form-control wpcf7-text" aria-invalid="false" placeholder="First Name" value="" type="text" name="your-name"></span><br>
                                    <span class="wpcf7-form-control-wrap" data-name="your-last-name"><input size="40" maxlength="400" class="wpcf7-form-control wpcf7-text" aria-invalid="false" placeholder="Last Name" value="" type="text" name="your-last-name"></span><br>
                                    <span class="wpcf7-form-control-wrap" data-name="your-email"><input size="40" maxlength="400" class="wpcf7-form-control wpcf7-email wpcf7-validates-as-required wpcf7-text wpcf7-validates-as-email" aria-required="true" aria-invalid="false" placeholder="E-mail" value="" type="email" name="your-email"></span><br>
                                    <span class="wpcf7-form-control-wrap" data-name="your-message"><textarea cols="40" rows="10" maxlength="2000" class="wpcf7-form-control wpcf7-textarea" aria-invalid="false" placeholder="Message" name="your-message"></textarea></span>
                                </p>
                                <div class="pr">
                                    <p><span class="wpcf7-form-control-wrap" data-name="file-475"><input size="40" class="wpcf7-form-control wpcf7-file inputfile js" id="file-2" accept=".pdf,.docx,.doc,.txt,.png" aria-invalid="false" type="file" name="file-475"></span>
                                        <label for="file-2"><span class="label-content">Add resume / PDF or Docx</span></label>
                                    </p>
                                </div>
                                <div class="dr">
                                    <p><span class="wpcf7-form-control-wrap" data-name="file-221"><input size="40" class="wpcf7-form-control wpcf7-file inputfile js" id="file-3" accept=".pdf,.docx,.doc,.txt,.png" aria-invalid="false" type="file" name="file-221"></span>
                                        <label for="file-3"><span class="label-content">Add cover letter</span></label>
                                    </p>
                                </div>

                                <div class="margin--top-l row">
                                    <p><input class="wpcf7-form-control wpcf7-submit has-spinner" type="submit" value="Submit application">
                                    </p>
                                </div>
                            </form>
                        </div>

                        <div>
                        </div>
                    </div>
                </article>
            </div>
        </section>

        <!-- Contact -->
        <section class="contact-us-section grid" style="background-image: url('static/picture/contact-bg-min.jpg');">
            <div id="contact-us-section-grid" class="container">
                <h3 class="header type--heading type--white type--underline-red active margin--bottom-s"> Contact us </h3>

                <article class="u-margin-bottom col-12">


                    <div class="col-12 col-s-6 margin--top-m col-l-5">
                        <div class="margin--bottom-m">
                            <div class="micro--addr-title margin--bottom-xs">Chicago Office </div>
                            <div>550 West Jackson Blvd, Suite 1400 </div>
                            <div>Chicago, Illinois 60661</div>
                            <div></div>

                            <a href="https://www.google.rs/maps/place/550+W+Jackson+Blvd+%231400,+Chicago,+IL+60661,+%D0%A1%D1%98%D0%B5%D0%B4%D0%B8%D1%9A%D0%B5%D0%BD%D0%B5+%D0%94%D1%80%D0%B6%D0%B0%D0%B2%D0%B5/@41.8777763,-87.6469499,15.25z/data=!4m5!3m4!1s0x880e2cc10885b721:0x27ec8f2a34616edb!8m2!3d41.8783242!4d-87.6414944"
                                class="btn blue margin--top-l"> Get driving directions </a>
                        </div>

                        <div class="micro--addr-title margin--bottom-xs">Amsterdam Office </div>
                        <div>Muiderstraat 9/U</div>
                        <div>1011 PZ Amsterdam, Netherlands</div>
                        <a href="https://www.google.com/maps/place/Muiderstraat+9u,+1011+PZ+Amsterdam,+Netherlands/@52.3679096,4.9061002,17z/data=!3m1!4b1!4m5!3m4!1s0x47c609bd169b619d:0xfc6497953fdc9389!8m2!3d52.3679096!4d4.9061002" class="btn blue margin--top-l"> Get driving directions </a>
                        <p></p>
                    </div>

                </article>
            </div>
        </section>


    </main>
    <!-- #main -->

    <script>
        var mapMarker = 'static/picture/mapMarker.png';
        var Hotellocation = {
            lat: 41.878150,
            lng: -87.641295
        };
        //
    </script>

    <script>
        window.mapsLoaded = false;
        window.onMapsLoadedListeners = [];
        window.onMapsLoad = function(cb) {
            if (window.mapsLoaded) {
                setTimeout(cb, 0);
            } else {
                window.onMapsLoadedListeners.push(cb);
            }
        }
        window.mapsOnLoad = function() {
            window.mapsLoaded = true;
            window.onMapsLoadedListeners.forEach(function(cb) {
                cb();
            })


        }
    </script>



    <script type="text/javascript" src="static/js/hooks.min.js" id="wp-hooks-js"></script>
    <script type="text/javascript" src="static/js/i18n.min.js" id="wp-i18n-js"></script>
    <script type="text/javascript" id="wp-i18n-js-after">
        /* <![CDATA[ */
        wp.i18n.setLocaleData({
            'text direction\u0004ltr': ['ltr']
        });
        /* ]]> */
    </script>
    <script type="text/javascript" src="static/js/index-6.1.1.js" id="swv-js"></script>
    <script type="text/javascript" id="contact-form-7-js-before">
        /* <![CDATA[ */
        var wpcf7 = {
            "api": {
                "root": "\/index.php?rest_route=\/",
                "namespace": "contact-form-7\/v1"
            }
        };
        /* ]]> */
    </script>
    <script type="text/javascript" src="static/js/index-6.1.11.js" id="contact-form-7-js"></script>
    <script type="text/javascript" src="static/js/app.js" id="app-scripts-xpro-js"></script>
    <script type="text/javascript" src="static/js/api.js" id="google-recaptcha-js"></script>
    <script type="text/javascript" src="static/js/wp-polyfill.min.js" id="wp-polyfill-js"></script>
    <script type="text/javascript" id="wpcf7-recaptcha-js-before">
        /* <![CDATA[ */
        var wpcf7_recaptcha = {
            "sitekey": "6LcNmYoUAAAAAGrHSoFLHkxsxz_l7Y_3F9FGpT7s",
            "actions": {
                "homepage": "homepage",
                "contactform": "contactform"
            }
        };
        /* ]]> */
    </script>
    <script type="text/javascript" src="static/js/index-6.1.12.js" id="wpcf7-recaptcha-js"></script>
    <footer>
    </footer>
</body>

</html>